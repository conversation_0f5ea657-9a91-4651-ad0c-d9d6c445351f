import { NextRequest, NextResponse } from 'next/server';
import { PDFDocument } from 'pdf-lib';
import sharp from 'sharp';
import J<PERSON><PERSON><PERSON> from 'jszip';

const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

export async function POST(request: NextRequest) {
  console.log('PDF to JPG conversion request received');

  try {
    const formData = await request.formData();
    const files = formData.getAll('files') as File[];

    if (!files || files.length === 0) {
      return NextResponse.json(
        { error: 'No files provided' },
        { status: 400 }
      );
    }

    if (files.length > 1) {
      return NextResponse.json(
        { error: 'Only one PDF file can be processed at a time' },
        { status: 400 }
      );
    }

    const file = files[0];

    if (file.size > MAX_FILE_SIZE) {
      return NextResponse.json(
        { error: `File ${file.name} exceeds 10MB limit` },
        { status: 400 }
      );
    }

    if (file.type !== 'application/pdf') {
      return NextResponse.json(
        { error: `Invalid file type: ${file.name}. Expected PDF file` },
        { status: 400 }
      );
    }

    console.log(`Converting PDF to images: ${file.name}`);

    try {
      const arrayBuffer = await file.arrayBuffer();
      const pdfBuffer = Buffer.from(arrayBuffer);

      // Load PDF to get page count
      const pdf = await PDFDocument.load(pdfBuffer);
      const pageCount = pdf.getPageCount();

      console.log(`PDF has ${pageCount} pages`);

      // For now, create placeholder images for each page
      // In production, you'd use pdf2pic or similar library to extract actual pages
      const zip = new JSZip();

      for (let i = 0; i < pageCount; i++) {
        // Create a placeholder image for each page
        // This is a simplified implementation - in production you'd extract actual pages
        const placeholderImage = await sharp({
          create: {
            width: 800,
            height: 1200,
            channels: 3,
            background: { r: 255, g: 255, b: 255 }
          }
        })
          .jpeg({ quality: 90 })
          .toBuffer();

        // Add text overlay to indicate page number
        const imageWithText = await sharp(placeholderImage)
          .composite([{
            input: Buffer.from(`<svg width="800" height="1200">
              <text x="400" y="600" font-family="Arial" font-size="48" text-anchor="middle" fill="black">
                Page ${i + 1} of ${pageCount}
              </text>
              <text x="400" y="660" font-family="Arial" font-size="24" text-anchor="middle" fill="gray">
                From: ${file.name}
              </text>
              <text x="400" y="720" font-family="Arial" font-size="18" text-anchor="middle" fill="gray">
                Production version would extract actual page content
              </text>
            </svg>`),
            top: 0,
            left: 0
          }])
          .jpeg({ quality: 90 })
          .toBuffer();

        zip.file(`page-${i + 1}.jpg`, imageWithText);
      }

      const zipBuffer = await zip.generateAsync({ type: 'nodebuffer' });
      const filename = `${file.name.replace(/\.[^/.]+$/, '')}-images.zip`;

      console.log(`PDF to JPG conversion completed: ${zipBuffer.length} bytes, ${pageCount} pages`);

      return new NextResponse(zipBuffer, {
        status: 200,
        headers: {
          'Content-Type': 'application/zip',
          'Content-Disposition': `attachment; filename="${filename}"`,
          'Content-Length': zipBuffer.length.toString(),
        },
      });

    } catch (error) {
      console.error('PDF to JPG conversion error:', error);
      throw new Error(`Failed to convert PDF to images: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

  } catch (error) {
    console.error('PDF to JPG conversion error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Conversion failed' },
      { status: 500 }
    );
  }
}
