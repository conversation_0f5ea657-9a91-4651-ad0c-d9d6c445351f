declare module 'docx-pdf' {
  interface ConvertOptions {
    output?: string;
  }

  interface ConvertCallback {
    (error: Error | null, result?: any): void;
  }

  function convert(
    input: string,
    callback: ConvertCallback
  ): void;

  function convert(
    input: string,
    output: string,
    callback: ConvertCallback
  ): void;

  function convert(
    input: string,
    options: ConvertOptions,
    callback: ConvertCallback
  ): void;

  export = convert;
}
