import { NextRequest, NextResponse } from 'next/server';
import { PDFDocument, rgb, StandardFonts } from 'pdf-lib';

const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

export async function POST(request: NextRequest) {
  console.log('PowerPoint to PDF conversion request received');

  try {
    const formData = await request.formData();
    const files = formData.getAll('files') as File[];

    if (!files || files.length === 0) {
      return NextResponse.json(
        { error: 'No files provided' },
        { status: 400 }
      );
    }

    // Validate files
    const validTypes = [
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'application/vnd.ms-powerpoint'
    ];

    for (const file of files) {
      if (file.size > MAX_FILE_SIZE) {
        return NextResponse.json(
          { error: `File ${file.name} exceeds 10MB limit` },
          { status: 400 }
        );
      }

      if (!validTypes.includes(file.type)) {
        return NextResponse.json(
          { error: `Invalid file type: ${file.name}. Expected PowerPoint file (.pptx, .ppt)` },
          { status: 400 }
        );
      }
    }

    console.log(`Converting ${files.length} PowerPoint file(s) to PDF`);

    const pdfDoc = await PDFDocument.create();
    const font = await pdfDoc.embedFont(StandardFonts.Helvetica);
    const boldFont = await pdfDoc.embedFont(StandardFonts.HelveticaBold);

    for (const file of files) {
      try {
        // For PowerPoint files, we'll create a placeholder PDF with file info
        // In production, you'd use a library like node-pptx or similar to extract actual content
        const page = pdfDoc.addPage([612, 792]);
        const margin = 50;
        let yPosition = 750;

        // Title
        page.drawText('PowerPoint Conversion', {
          x: margin,
          y: yPosition,
          size: 18,
          font: boldFont,
          color: rgb(0, 0, 0),
        });

        yPosition -= 40;

        // File information
        page.drawText(`File: ${file.name}`, {
          x: margin,
          y: yPosition,
          size: 12,
          font: font,
          color: rgb(0, 0, 0),
        });

        yPosition -= 20;

        page.drawText(`Size: ${(file.size / 1024).toFixed(2)} KB`, {
          x: margin,
          y: yPosition,
          size: 12,
          font: font,
          color: rgb(0, 0, 0),
        });

        yPosition -= 40;

        // Note about PowerPoint processing
        const noteText = [
          'PowerPoint file has been processed.',
          'This conversion extracts basic file information.',
          '',
          'For full PowerPoint content extraction, consider using:',
          '• PptxGenJS for reading PPTX files',
          '• node-pptx for comprehensive parsing',
          '• Custom PowerPoint processing libraries',
          '',
          'The actual slide content, images, and formatting',
          'would be extracted and rendered in a production system.'
        ];

        for (const line of noteText) {
          page.drawText(line, {
            x: margin,
            y: yPosition,
            size: 11,
            font: font,
            color: rgb(0.2, 0.2, 0.2),
          });
          yPosition -= 16;
        }

      } catch (error) {
        console.error(`Error processing ${file.name}:`, error);
        throw new Error(`Failed to process PowerPoint file ${file.name}`);
      }
    }

    const pdfBytes = await pdfDoc.save();
    const filename = files.length === 1 ?
      `${files[0].name.replace(/\.[^/.]+$/, '')}.pdf` :
      'converted-presentations.pdf';

    console.log(`PowerPoint to PDF conversion completed: ${pdfBytes.length} bytes`);

    return new NextResponse(Buffer.from(pdfBytes), {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="${filename}"`,
        'Content-Length': pdfBytes.length.toString(),
      },
    });

  } catch (error) {
    console.error('PowerPoint to PDF conversion error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Conversion failed' },
      { status: 500 }
    );
  }
}
