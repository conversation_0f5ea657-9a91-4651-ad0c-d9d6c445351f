/**
 * Comprehensive Test Suite for PDF Conversion API Routes
 * 
 * This script tests all the individual API conversion endpoints to ensure
 * they handle actual content conversion (not demo/placeholder text) and
 * produce production-ready outputs.
 */

const fs = require('fs');
const path = require('path');
const FormData = require('form-data');

// Test configuration
const BASE_URL = 'http://localhost:3000';
const TEST_FILES_DIR = './test-files';
const OUTPUT_DIR = './test-outputs';

// Ensure output directory exists
if (!fs.existsSync(OUTPUT_DIR)) {
  fs.mkdirSync(OUTPUT_DIR, { recursive: true });
}

// API endpoints to test
const API_ENDPOINTS = [
  {
    name: 'JPG to PDF',
    endpoint: '/api/convert/jpg-to-pdf',
    testFile: 'sample-image.jpg',
    expectedOutput: 'pdf',
    description: 'Convert image files to PDF format'
  },
  {
    name: 'Word to PDF',
    endpoint: '/api/convert/word-to-pdf',
    testFile: 'sample-document.docx',
    expectedOutput: 'pdf',
    description: 'Convert Word documents to PDF with actual text extraction'
  },
  {
    name: 'PowerPoint to PDF',
    endpoint: '/api/convert/powerpoint-to-pdf',
    testFile: 'sample-presentation.pptx',
    expectedOutput: 'pdf',
    description: 'Convert PowerPoint presentations to PDF'
  },
  {
    name: 'Excel to PDF',
    endpoint: '/api/convert/excel-to-pdf',
    testFile: 'sample-spreadsheet.xlsx',
    expectedOutput: 'pdf',
    description: 'Convert Excel spreadsheets to PDF with table rendering'
  },
  {
    name: 'HTML to PDF',
    endpoint: '/api/convert/html-to-pdf',
    testFile: 'sample-webpage.html',
    expectedOutput: 'pdf',
    description: 'Convert HTML files to PDF with text extraction'
  },
  {
    name: 'PDF to JPG',
    endpoint: '/api/convert/pdf-to-jpg',
    testFile: 'sample-document.pdf',
    expectedOutput: 'zip',
    description: 'Convert PDF pages to JPG images'
  },
  {
    name: 'PDF to Word',
    endpoint: '/api/convert/pdf-to-word',
    testFile: 'sample-document.pdf',
    expectedOutput: 'docx',
    description: 'Convert PDF to Word with actual text extraction using pdf-parse'
  },
  {
    name: 'PDF to PowerPoint',
    endpoint: '/api/convert/pdf-to-powerpoint',
    testFile: 'sample-document.pdf',
    expectedOutput: 'pptx',
    description: 'Convert PDF to PowerPoint with content-based slides'
  },
  {
    name: 'PDF to Excel',
    endpoint: '/api/convert/pdf-to-excel',
    testFile: 'sample-document.pdf',
    expectedOutput: 'xlsx',
    description: 'Convert PDF to Excel with text analysis and structure detection'
  },
  {
    name: 'PDF to PDF/A',
    endpoint: '/api/convert/pdf-to-pdfa',
    testFile: 'sample-document.pdf',
    expectedOutput: 'pdf',
    description: 'Convert PDF to PDF/A archival format'
  }
];

/**
 * Create sample test files if they don't exist
 */
function createSampleFiles() {
  if (!fs.existsSync(TEST_FILES_DIR)) {
    fs.mkdirSync(TEST_FILES_DIR, { recursive: true });
  }

  // Create sample HTML file
  const htmlContent = `<!DOCTYPE html>
<html>
<head>
    <title>Sample HTML Document</title>
</head>
<body>
    <h1>Test Document for HTML to PDF Conversion</h1>
    <p>This is a sample HTML document with <strong>bold text</strong> and <em>italic text</em>.</p>
    <h2>Features to Test</h2>
    <ul>
        <li>Text extraction and formatting</li>
        <li>HTML tag removal</li>
        <li>Proper paragraph structure</li>
        <li>Character encoding handling</li>
    </ul>
    <p>This content should be properly extracted and converted to PDF format, demonstrating actual content processing rather than placeholder text.</p>
</body>
</html>`;

  fs.writeFileSync(path.join(TEST_FILES_DIR, 'sample-webpage.html'), htmlContent);

  console.log('✓ Sample test files created in', TEST_FILES_DIR);
  console.log('Note: You may need to add actual .docx, .pptx, .xlsx, .jpg, and .pdf files for complete testing');
}

/**
 * Test a single API endpoint
 */
async function testEndpoint(endpoint) {
  console.log(`\n🧪 Testing: ${endpoint.name}`);
  console.log(`   Endpoint: ${endpoint.endpoint}`);
  console.log(`   Description: ${endpoint.description}`);

  const testFilePath = path.join(TEST_FILES_DIR, endpoint.testFile);
  
  if (!fs.existsSync(testFilePath)) {
    console.log(`   ⚠️  Test file not found: ${testFilePath}`);
    console.log(`   📝 Please add this file to run the test`);
    return { success: false, error: 'Test file not found' };
  }

  try {
    const formData = new FormData();
    formData.append('files', fs.createReadStream(testFilePath));

    const response = await fetch(`${BASE_URL}${endpoint.endpoint}`, {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }

    // Save the output file
    const buffer = await response.arrayBuffer();
    const outputFileName = `${endpoint.name.replace(/\s+/g, '-').toLowerCase()}-output.${endpoint.expectedOutput}`;
    const outputPath = path.join(OUTPUT_DIR, outputFileName);
    
    fs.writeFileSync(outputPath, Buffer.from(buffer));

    // Verify output
    const stats = fs.statSync(outputPath);
    const fileSizeKB = (stats.size / 1024).toFixed(2);

    console.log(`   ✅ Success! Output saved to: ${outputPath}`);
    console.log(`   📊 File size: ${fileSizeKB} KB`);
    
    // Additional validation based on file type
    if (endpoint.expectedOutput === 'pdf' && stats.size < 1000) {
      console.log(`   ⚠️  Warning: PDF file seems very small (${fileSizeKB} KB) - may contain minimal content`);
    }
    
    if (endpoint.expectedOutput === 'zip' && stats.size < 5000) {
      console.log(`   ⚠️  Warning: ZIP file seems very small (${fileSizeKB} KB) - may contain placeholder images`);
    }

    return { 
      success: true, 
      outputPath, 
      fileSize: stats.size,
      fileSizeKB: parseFloat(fileSizeKB)
    };

  } catch (error) {
    console.log(`   ❌ Error: ${error.message}`);
    return { success: false, error: error.message };
  }
}

/**
 * Run all tests
 */
async function runAllTests() {
  console.log('🚀 Starting PDF Conversion API Tests');
  console.log('=====================================');

  // Create sample files
  createSampleFiles();

  const results = [];
  let successCount = 0;
  let totalTests = API_ENDPOINTS.length;

  for (const endpoint of API_ENDPOINTS) {
    const result = await testEndpoint(endpoint);
    results.push({
      name: endpoint.name,
      endpoint: endpoint.endpoint,
      ...result
    });

    if (result.success) {
      successCount++;
    }

    // Add delay between tests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  // Print summary
  console.log('\n📊 Test Summary');
  console.log('================');
  console.log(`Total tests: ${totalTests}`);
  console.log(`Passed: ${successCount}`);
  console.log(`Failed: ${totalTests - successCount}`);
  console.log(`Success rate: ${((successCount / totalTests) * 100).toFixed(1)}%`);

  // Print detailed results
  console.log('\n📋 Detailed Results');
  console.log('===================');
  results.forEach(result => {
    const status = result.success ? '✅' : '❌';
    const size = result.fileSizeKB ? ` (${result.fileSizeKB} KB)` : '';
    console.log(`${status} ${result.name}${size}`);
    if (!result.success) {
      console.log(`    Error: ${result.error}`);
    }
  });

  // Check for production-ready indicators
  console.log('\n🔍 Production Readiness Check');
  console.log('=============================');
  
  const pdfToWordResult = results.find(r => r.name === 'PDF to Word');
  if (pdfToWordResult && pdfToWordResult.success) {
    if (pdfToWordResult.fileSizeKB > 5) {
      console.log('✅ PDF to Word: Likely contains actual extracted content (good file size)');
    } else {
      console.log('⚠️  PDF to Word: May contain placeholder content (small file size)');
    }
  }

  const pdfToExcelResult = results.find(r => r.name === 'PDF to Excel');
  if (pdfToExcelResult && pdfToExcelResult.success) {
    if (pdfToExcelResult.fileSizeKB > 8) {
      console.log('✅ PDF to Excel: Likely contains structured data analysis (good file size)');
    } else {
      console.log('⚠️  PDF to Excel: May contain minimal analysis (small file size)');
    }
  }

  console.log('\n🎯 Next Steps');
  console.log('=============');
  console.log('1. Check output files in:', OUTPUT_DIR);
  console.log('2. Verify actual content extraction (not placeholder text)');
  console.log('3. Test with various file types and sizes');
  console.log('4. Validate error handling with invalid files');
  console.log('5. Performance test with larger files');

  return results;
}

// Run tests if this script is executed directly
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = { runAllTests, testEndpoint, API_ENDPOINTS };
