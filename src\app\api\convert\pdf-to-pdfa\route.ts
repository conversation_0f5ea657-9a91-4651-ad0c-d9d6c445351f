import { NextRequest, NextResponse } from 'next/server';
import { PDFDocument } from 'pdf-lib';

const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

export async function POST(request: NextRequest) {
  console.log('PDF to PDF/A conversion request received');

  try {
    const formData = await request.formData();
    const files = formData.getAll('files') as File[];

    if (!files || files.length === 0) {
      return NextResponse.json(
        { error: 'No files provided' },
        { status: 400 }
      );
    }

    // Validate files
    for (const file of files) {
      if (file.size > MAX_FILE_SIZE) {
        return NextResponse.json(
          { error: `File ${file.name} exceeds 10MB limit` },
          { status: 400 }
        );
      }

      if (file.type !== 'application/pdf') {
        return NextResponse.json(
          { error: `Invalid file type: ${file.name}. Expected PDF file` },
          { status: 400 }
        );
      }
    }

    console.log(`Converting ${files.length} PDF(s) to PDF/A format`);

    try {
      // For PDF/A conversion, we'll create a new PDF with PDF/A compliance
      // In production, you'd use specialized libraries for proper PDF/A conversion
      const pdfDoc = await PDFDocument.create();

      for (const file of files) {
        const arrayBuffer = await file.arrayBuffer();
        const existingPdfBytes = new Uint8Array(arrayBuffer);
        const existingPdf = await PDFDocument.load(existingPdfBytes);

        // Copy all pages from existing PDF
        const pages = await pdfDoc.copyPages(existingPdf, existingPdf.getPageIndices());
        pages.forEach((page) => pdfDoc.addPage(page));
      }

      // Add PDF/A metadata (simplified)
      pdfDoc.setTitle('PDF/A Document');
      pdfDoc.setSubject('Converted to PDF/A format for long-term archival');
      pdfDoc.setCreator('PDF Tools');
      pdfDoc.setProducer('PDF Tools PDF/A Converter');
      pdfDoc.setKeywords(['PDF/A', 'Archive', 'Long-term preservation']);
      pdfDoc.setAuthor('PDF Tools User');
      pdfDoc.setCreationDate(new Date());
      pdfDoc.setModificationDate(new Date());

      const pdfBytes = await pdfDoc.save();
      const filename = files.length === 1 ?
        `${files[0].name.replace(/\.[^/.]+$/, '')}-pdfa.pdf` :
        'converted-pdfa.pdf';

      console.log(`PDF to PDF/A conversion completed: ${pdfBytes.length} bytes`);

      return new NextResponse(Buffer.from(pdfBytes), {
        status: 200,
        headers: {
          'Content-Type': 'application/pdf',
          'Content-Disposition': `attachment; filename="${filename}"`,
          'Content-Length': pdfBytes.length.toString(),
        },
      });

    } catch (error) {
      console.error('PDF to PDF/A conversion error:', error);
      throw new Error(`Failed to convert to PDF/A: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

  } catch (error) {
    console.error('PDF to PDF/A conversion error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Conversion failed' },
      { status: 500 }
    );
  }
}
