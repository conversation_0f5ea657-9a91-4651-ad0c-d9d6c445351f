import { NextRequest, NextResponse } from 'next/server';
import { PDFDocument, rgb, StandardFonts } from 'pdf-lib';
import * as XLSX from 'xlsx';

const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

export async function POST(request: NextRequest) {
  console.log('Excel to PDF conversion request received');

  try {
    const formData = await request.formData();
    const files = formData.getAll('files') as File[];

    if (!files || files.length === 0) {
      return NextResponse.json(
        { error: 'No files provided' },
        { status: 400 }
      );
    }

    // Validate files
    const validTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel'
    ];

    for (const file of files) {
      if (file.size > MAX_FILE_SIZE) {
        return NextResponse.json(
          { error: `File ${file.name} exceeds 10MB limit` },
          { status: 400 }
        );
      }

      if (!validTypes.includes(file.type)) {
        return NextResponse.json(
          { error: `Invalid file type: ${file.name}. Expected Excel file (.xlsx, .xls)` },
          { status: 400 }
        );
      }
    }

    console.log(`Converting ${files.length} Excel file(s) to PDF`);

    const pdfDoc = await PDFDocument.create();
    const font = await pdfDoc.embedFont(StandardFonts.Helvetica);
    const boldFont = await pdfDoc.embedFont(StandardFonts.HelveticaBold);

    for (const file of files) {
      try {
        const arrayBuffer = await file.arrayBuffer();
        const workbook = XLSX.read(arrayBuffer, { type: 'array' });

        // Process each worksheet
        for (const sheetName of workbook.SheetNames) {
          const worksheet = workbook.Sheets[sheetName];
          const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1, defval: '' }) as any[][];

          if (jsonData.length === 0) continue;

          // Create a new page for each sheet
          const page = pdfDoc.addPage([792, 612]); // Landscape for better table display
          let yPosition = 570;
          const margin = 30;
          const cellHeight = 20;
          const cellWidth = 100;

          // Draw sheet title
          page.drawText(`Sheet: ${sheetName}`, {
            x: margin,
            y: yPosition,
            size: 14,
            font: boldFont,
            color: rgb(0, 0, 0),
          });

          yPosition -= 30;

          // Draw table data
          for (let rowIndex = 0; rowIndex < Math.min(jsonData.length, 25); rowIndex++) { // Limit rows to fit page
            const row = jsonData[rowIndex];
            let xPosition = margin;

            for (let colIndex = 0; colIndex < Math.min(row.length, 7); colIndex++) { // Limit columns to fit page
              const cellValue = String(row[colIndex] || '').substring(0, 15); // Truncate long values

              // Draw cell border
              page.drawRectangle({
                x: xPosition,
                y: yPosition - cellHeight,
                width: cellWidth,
                height: cellHeight,
                borderColor: rgb(0, 0, 0),
                borderWidth: 1,
              });

              // Draw cell content
              if (cellValue) {
                page.drawText(cellValue, {
                  x: xPosition + 5,
                  y: yPosition - 15,
                  size: 10,
                  font: rowIndex === 0 ? boldFont : font, // Bold for header row
                  color: rgb(0, 0, 0),
                });
              }

              xPosition += cellWidth;
            }

            yPosition -= cellHeight;

            // Check if we need a new page
            if (yPosition < 50) {
              break; // Stop adding rows if we run out of space
            }
          }
        }

      } catch (error) {
        console.error(`Error processing ${file.name}:`, error);
        throw new Error(`Failed to process Excel file ${file.name}`);
      }
    }

    const pdfBytes = await pdfDoc.save();
    const filename = files.length === 1 ?
      `${files[0].name.replace(/\.[^/.]+$/, '')}.pdf` :
      'converted-spreadsheets.pdf';

    console.log(`Excel to PDF conversion completed: ${pdfBytes.length} bytes`);

    return new NextResponse(Buffer.from(pdfBytes), {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="${filename}"`,
        'Content-Length': pdfBytes.length.toString(),
      },
    });

  } catch (error) {
    console.error('Excel to PDF conversion error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Conversion failed' },
      { status: 500 }
    );
  }
}
