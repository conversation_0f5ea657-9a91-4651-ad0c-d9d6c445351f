'use client';

import { useCallback, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Upload, File, X, CheckCircle, AlertCircle } from 'lucide-react';
import { Button } from '@/src/components/ui/button';
import { Progress } from '@/src/components/ui/progress';

interface UploadedFile {
  id: string;
  file: File;
  progress: number;
  status: 'uploading' | 'complete' | 'error';
  error?: string;
}

interface FileUploadZoneProps {
  onFilesSelected: (files: File[]) => void;
  accept?: string;
  multiple?: boolean;
  maxSize?: number; // in MB
}

export default function FileUploadZone({
  onFilesSelected,
  accept = '.pdf',
  multiple = true,
  maxSize = 10
}: FileUploadZoneProps) {
  const [dragActive, setDragActive] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);

  console.log('FileUploadZone rendered with', uploadedFiles.length, 'files');

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    const files = Array.from(e.dataTransfer.files);
    handleFiles(files);
  }, []);

  const handleFileInput = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    handleFiles(files);
  }, []);

  const handleFiles = (files: File[]) => {
    console.log('Processing', files.length, 'files');

    const processedFiles: UploadedFile[] = [];
    const validFiles: File[] = [];

    files.forEach(file => {
      const fileId = Math.random().toString(36).substr(2, 9);

      // Validate file size
      if (file.size > maxSize * 1024 * 1024) {
        processedFiles.push({
          id: fileId,
          file,
          progress: 0,
          status: 'error',
          error: `File too large (${(file.size / (1024 * 1024)).toFixed(1)}MB). Max size: ${maxSize}MB`
        });
        return;
      }

      // Validate file type if accept is specified
      if (accept && accept !== '*') {
        const acceptedTypes = accept.split(',').map(type => type.trim());
        const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
        const mimeType = file.type;

        const isValidType = acceptedTypes.some(acceptType => {
          if (acceptType.startsWith('.')) {
            return fileExtension === acceptType.toLowerCase();
          }
          return mimeType.startsWith(acceptType.replace('*', ''));
        });

        if (!isValidType) {
          processedFiles.push({
            id: fileId,
            file,
            progress: 0,
            status: 'error',
            error: `Invalid file type. Expected: ${accept}`
          });
          return;
        }
      }

      // File is valid
      processedFiles.push({
        id: fileId,
        file,
        progress: 0,
        status: 'uploading'
      });
      validFiles.push(file);
    });

    setUploadedFiles(prev => [...prev, ...processedFiles]);

    // Simulate upload progress for valid files
    processedFiles
      .filter(f => f.status === 'uploading')
      .forEach((uploadFile, index) => {
        setTimeout(() => {
          const interval = setInterval(() => {
            setUploadedFiles(prev =>
              prev.map(f =>
                f.id === uploadFile.id
                  ? { ...f, progress: Math.min(f.progress + 10, 100) }
                  : f
              )
            );
          }, 100);

          setTimeout(() => {
            clearInterval(interval);
            setUploadedFiles(prev =>
              prev.map(f =>
                f.id === uploadFile.id
                  ? { ...f, progress: 100, status: 'complete' }
                  : f
              )
            );
          }, 1000);
        }, index * 200);
      });

    // Only pass valid files to parent
    if (validFiles.length > 0) {
      onFilesSelected(validFiles);
    }
  };

  const removeFile = (id: string) => {
    console.log('Removing file:', id);
    setUploadedFiles(prev => prev.filter(f => f.id !== id));
  };

  return (
    <div className="w-full max-w-2xl mx-auto">
      {/* Upload Zone */}
      <motion.div
        className={`relative border-2 border-dashed rounded-xl p-8 text-center transition-all duration-200 ${dragActive
          ? 'border-blue-500 bg-blue-50 dark:bg-blue-950/20'
          : 'border-gray-300 dark:border-gray-600 hover:border-blue-400'
          }`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
        whileHover={{ scale: 1.01 }}
      >
        <input
          type="file"
          multiple={multiple}
          accept={accept}
          onChange={handleFileInput}
          className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
        />

        <div className="flex flex-col items-center space-y-4">
          <motion.div
            animate={dragActive ? { scale: 1.1 } : { scale: 1 }}
            className="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center"
          >
            <Upload className="h-8 w-8 text-white" />
          </motion.div>

          <div>
            <h3 className="text-lg font-semibold text-navy-900 dark:text-white">
              Drop your files here
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mt-1">
              or click to browse • {accept} files • Max {maxSize}MB each
            </p>
          </div>

          <Button variant="outline" className="mt-2">
            Choose Files
          </Button>
        </div>
      </motion.div>

      {/* File List */}
      <AnimatePresence>
        {uploadedFiles.length > 0 && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="mt-6 space-y-3"
          >
            <h4 className="font-medium text-navy-900 dark:text-white">
              Uploaded Files ({uploadedFiles.length})
            </h4>

            {uploadedFiles.map((uploadFile) => (
              <motion.div
                key={uploadFile.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 20 }}
                className="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
              >
                <div className="flex-shrink-0">
                  {uploadFile.status === 'complete' ? (
                    <CheckCircle className="h-5 w-5 text-green-500" />
                  ) : uploadFile.status === 'error' ? (
                    <AlertCircle className="h-5 w-5 text-red-500" />
                  ) : (
                    <File className="h-5 w-5 text-blue-500" />
                  )}
                </div>

                <div className="flex-1 min-w-0">
                  <p className={`text-sm font-medium truncate ${uploadFile.status === 'error'
                      ? 'text-red-600 dark:text-red-400'
                      : 'text-gray-900 dark:text-white'
                    }`}>
                    {uploadFile.file.name}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {(uploadFile.file.size / (1024 * 1024)).toFixed(1)} MB
                  </p>

                  {uploadFile.status === 'uploading' && (
                    <Progress value={uploadFile.progress} className="mt-1 h-1" />
                  )}

                  {uploadFile.status === 'error' && uploadFile.error && (
                    <p className="text-xs text-red-500 mt-1">
                      {uploadFile.error}
                    </p>
                  )}
                </div>

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => removeFile(uploadFile.id)}
                  className="flex-shrink-0"
                >
                  <X className="h-4 w-4" />
                </Button>
              </motion.div>
            ))}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}