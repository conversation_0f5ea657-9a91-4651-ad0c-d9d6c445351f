import { NextRequest, NextResponse } from 'next/server';
import { convertDocxToPdf } from '@nativedocuments/docx-wasm';

const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

export async function POST(request: NextRequest) {
  console.log('Word to PDF conversion request received');

  try {
    const formData = await request.formData();
    const files = formData.getAll('files') as File[];

    if (!files || files.length === 0) {
      return NextResponse.json(
        { error: 'No files provided' },
        { status: 400 }
      );
    }

    // Validate files
    const validTypes = [
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/msword'
    ];

    for (const file of files) {
      if (file.size > MAX_FILE_SIZE) {
        return NextResponse.json(
          { error: `File ${file.name} exceeds 10MB limit` },
          { status: 400 }
        );
      }

      if (!validTypes.includes(file.type)) {
        return NextResponse.json(
          { error: `Invalid file type: ${file.name}. Expected Word document (.docx, .doc)` },
          { status: 400 }
        );
      }
    }

    console.log(`Converting ${files.length} Word document(s) to PDF`);

    // For single file conversion with full layout preservation
    if (files.length === 1) {
      const file = files[0];

      try {
        const arrayBuffer = await file.arrayBuffer();

        // Convert DOCX to PDF using docx-wasm (preserves full layout)
        const pdfBuffer = await convertDocxToPdf(arrayBuffer, {
          pageSize: 'A4',
          orientation: 'portrait',
          margins: {
            top: 20,
            bottom: 20,
            left: 20,
            right: 20
          }
        });

        const filename = `${file.name.replace(/\.[^/.]+$/, '')}.pdf`;

        console.log(`Word to PDF conversion completed: ${pdfBuffer.length} bytes`);

        return new NextResponse(Buffer.from(pdfBuffer), {
          status: 200,
          headers: {
            'Content-Type': 'application/pdf',
            'Content-Disposition': `attachment; filename="${filename}"`,
            'Content-Length': pdfBuffer.length.toString(),
          },
        });

      } catch (error) {
        console.error(`Error processing ${file.name}:`, error);
        throw new Error(`Failed to process Word document ${file.name}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    // For multiple files, we'll need to process them individually and merge
    // Since docx-wasm doesn't support merging, we'll fall back to the mammoth + puppeteer approach
    else {
      return NextResponse.json(
        { error: 'Multiple file conversion with full layout preservation is not yet supported. Please convert one file at a time for best results.' },
        { status: 400 }
      );
    }

  } catch (error) {
    console.error('Word to PDF conversion error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Conversion failed' },
      { status: 500 }
    );
  }
}
