import { NextRequest, NextResponse } from 'next/server';
import mammoth from 'mammoth';
import puppeteer from 'puppeteer';

const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

export async function POST(request: NextRequest) {
  console.log('Word to PDF conversion request received');

  try {
    const formData = await request.formData();
    const files = formData.getAll('files') as File[];

    if (!files || files.length === 0) {
      return NextResponse.json(
        { error: 'No files provided' },
        { status: 400 }
      );
    }

    // Validate files
    const validTypes = [
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/msword'
    ];

    for (const file of files) {
      if (file.size > MAX_FILE_SIZE) {
        return NextResponse.json(
          { error: `File ${file.name} exceeds 10MB limit` },
          { status: 400 }
        );
      }

      if (!validTypes.includes(file.type)) {
        return NextResponse.json(
          { error: `Invalid file type: ${file.name}. Expected Word document (.docx, .doc)` },
          { status: 400 }
        );
      }
    }

    console.log(`Converting ${files.length} Word document(s) to PDF`);

    // For single file conversion with full layout preservation
    if (files.length === 1) {
      const file = files[0];

      try {
        const arrayBuffer = await file.arrayBuffer();
        const buffer = Buffer.from(arrayBuffer);

        // Convert DOCX to HTML using mammoth with enhanced formatting preservation
        console.log('Converting DOCX to PDF using mammoth + puppeteer...');
        const result = await mammoth.convertToHtml({ buffer }, {
          styleMap: [
            "p[style-name='Heading 1'] => h1:fresh",
            "p[style-name='Title'] => h1.title:fresh",
            "r[style-name='Strong'] => strong"
          ]
        });

        let html = result.value;
        if (!html || html.trim().length === 0) {
          throw new Error(`No content found in ${file.name}`);
        }

        // Create a complete HTML document with enhanced styling
        const fullHtml = `
          <!DOCTYPE html>
          <html>
            <head>
              <meta charset="UTF-8">
              <title>Converted Document</title>
              <style>
                @page {
                  margin: 2cm;
                  size: A4;
                }
                body {
                  font-family: 'Times New Roman', serif;
                  font-size: 12pt;
                  line-height: 1.5;
                  color: #000;
                  margin: 0;
                  padding: 0;
                }
                h1, h2, h3, h4, h5, h6 {
                  color: #000;
                  margin-top: 1em;
                  margin-bottom: 0.5em;
                  page-break-after: avoid;
                }
                h1.title {
                  text-align: center;
                  font-size: 18pt;
                  font-weight: bold;
                  margin-bottom: 1em;
                }
                h2.subtitle {
                  text-align: center;
                  font-size: 14pt;
                  margin-bottom: 1.5em;
                }
                p {
                  margin-bottom: 0.5em;
                  text-align: justify;
                  orphans: 2;
                  widows: 2;
                }
                table {
                  border-collapse: collapse;
                  width: 100%;
                  margin: 1em 0;
                  page-break-inside: avoid;
                }
                table, th, td {
                  border: 1px solid #000;
                }
                th, td {
                  padding: 0.5em;
                  text-align: left;
                  vertical-align: top;
                }
                th {
                  background-color: #f0f0f0;
                  font-weight: bold;
                }
                strong {
                  font-weight: bold;
                }
                em {
                  font-style: italic;
                }
                ul, ol {
                  margin: 0.5em 0;
                  padding-left: 2em;
                }
                li {
                  margin-bottom: 0.25em;
                }
              </style>
            </head>
            <body>
              ${html}
            </body>
          </html>
        `;

        // Use Puppeteer to convert HTML to PDF
        const browser = await puppeteer.launch({
          headless: true,
          args: ['--no-sandbox', '--disable-setuid-sandbox', '--disable-dev-shm-usage']
        });

        try {
          const page = await browser.newPage();
          await page.setContent(fullHtml, { waitUntil: 'networkidle0' });

          const pdfBuffer = await page.pdf({
            format: 'A4',
            margin: {
              top: '2cm',
              right: '2cm',
              bottom: '2cm',
              left: '2cm'
            },
            printBackground: true,
            preferCSSPageSize: true
          });

          await browser.close();

          const filename = `${file.name.replace(/\.[^/.]+$/, '')}.pdf`;
          console.log(`Mammoth + Puppeteer conversion completed: ${pdfBuffer.length} bytes`);

          return new NextResponse(pdfBuffer, {
            status: 200,
            headers: {
              'Content-Type': 'application/pdf',
              'Content-Disposition': `attachment; filename="${filename}"`,
              'Content-Length': pdfBuffer.length.toString(),
            },
          });

        } catch (puppeteerError) {
          await browser.close();
          throw puppeteerError;
        }

      } catch (error) {
        console.error(`Error processing ${file.name}:`, error);
        throw new Error(`Failed to process Word document ${file.name}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    // For multiple files, we'll need to process them individually and merge
    // Since LibreOffice conversion doesn't support merging, we'll process each separately
    else {
      return NextResponse.json(
        { error: 'Multiple file conversion with full layout preservation is not yet supported. Please convert one file at a time for best results.' },
        { status: 400 }
      );
    }

  } catch (error) {
    console.error('Word to PDF conversion error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Conversion failed' },
      { status: 500 }
    );
  }
}

