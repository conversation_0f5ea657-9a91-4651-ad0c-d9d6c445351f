import { NextRequest, NextResponse } from 'next/server';
import docxConverter from 'docx-pdf';
import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';

const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

// Promisify the docx-pdf converter
const convertAsync = (inputPath: string, outputPath: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    docxConverter(inputPath, outputPath, (error: Error | null) => {
      if (error) {
        reject(error);
      } else {
        resolve();
      }
    });
  });
};

export async function POST(request: NextRequest) {
  console.log('Word to PDF conversion request received');

  try {
    const formData = await request.formData();
    const files = formData.getAll('files') as File[];

    if (!files || files.length === 0) {
      return NextResponse.json(
        { error: 'No files provided' },
        { status: 400 }
      );
    }

    // Validate files
    const validTypes = [
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/msword'
    ];

    for (const file of files) {
      if (file.size > MAX_FILE_SIZE) {
        return NextResponse.json(
          { error: `File ${file.name} exceeds 10MB limit` },
          { status: 400 }
        );
      }

      if (!validTypes.includes(file.type)) {
        return NextResponse.json(
          { error: `Invalid file type: ${file.name}. Expected Word document (.docx, .doc)` },
          { status: 400 }
        );
      }
    }

    console.log(`Converting ${files.length} Word document(s) to PDF`);

    // For single file conversion
    if (files.length === 1) {
      const file = files[0];
      const tempDir = path.join(process.cwd(), 'temp');

      // Ensure temp directory exists
      if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
      }

      const inputId = uuidv4();
      const inputPath = path.join(tempDir, `${inputId}.docx`);
      const outputPath = path.join(tempDir, `${inputId}.pdf`);

      try {
        // Write input file to temp directory
        const arrayBuffer = await file.arrayBuffer();
        const buffer = Buffer.from(arrayBuffer);
        fs.writeFileSync(inputPath, buffer);

        // Convert using docx-pdf
        await convertAsync(inputPath, outputPath);

        // Read the converted PDF
        const pdfBuffer = fs.readFileSync(outputPath);
        const filename = `${file.name.replace(/\.[^/.]+$/, '')}.pdf`;

        // Clean up temp files
        fs.unlinkSync(inputPath);
        fs.unlinkSync(outputPath);

        console.log(`Word to PDF conversion completed: ${pdfBuffer.length} bytes`);

        return new NextResponse(pdfBuffer, {
          status: 200,
          headers: {
            'Content-Type': 'application/pdf',
            'Content-Disposition': `attachment; filename="${filename}"`,
            'Content-Length': pdfBuffer.length.toString(),
          },
        });

      } catch (conversionError) {
        // Clean up temp files on error
        try {
          if (fs.existsSync(inputPath)) fs.unlinkSync(inputPath);
          if (fs.existsSync(outputPath)) fs.unlinkSync(outputPath);
        } catch (cleanupError) {
          console.warn('Failed to clean up temp files:', cleanupError);
        }
        throw conversionError;
      }
    }

    // For multiple files, we'll need to handle them differently
    // docx-pdf doesn't support merging, so we'll convert each separately
    // and return the first one for now (or implement a zip response)
    else {
      return NextResponse.json(
        { error: 'Multiple file conversion not yet supported with docx-pdf. Please convert one file at a time.' },
        { status: 400 }
      );
    }

  } catch (error) {
    console.error('Word to PDF conversion error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Conversion failed' },
      { status: 500 }
    );
  }
}
