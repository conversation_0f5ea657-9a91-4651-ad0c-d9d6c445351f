import { NextRequest, NextResponse } from 'next/server';
import mammoth from 'mammoth';
import puppeteer from 'puppeteer';

const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

export async function POST(request: NextRequest) {
  console.log('Word to PDF conversion request received');

  try {
    const formData = await request.formData();
    const files = formData.getAll('files') as File[];

    if (!files || files.length === 0) {
      return NextResponse.json(
        { error: 'No files provided' },
        { status: 400 }
      );
    }

    // Validate files
    const validTypes = [
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/msword'
    ];

    for (const file of files) {
      if (file.size > MAX_FILE_SIZE) {
        return NextResponse.json(
          { error: `File ${file.name} exceeds 10MB limit` },
          { status: 400 }
        );
      }

      if (!validTypes.includes(file.type)) {
        return NextResponse.json(
          { error: `Invalid file type: ${file.name}. Expected Word document (.docx, .doc)` },
          { status: 400 }
        );
      }
    }

    console.log(`Converting ${files.length} Word document(s) to PDF`);

    // For single file conversion with full layout preservation
    if (files.length === 1) {
      const file = files[0];

      try {
        const arrayBuffer = await file.arrayBuffer();
        const buffer = Buffer.from(arrayBuffer);

        // Convert DOCX to HTML using mammoth with enhanced formatting preservation and image extraction
        console.log('Converting DOCX to PDF using mammoth + puppeteer with image support...');

        const result = await mammoth.convertToHtml({ buffer }, {
          convertImage: mammoth.images.imgElement(function (image) {
            return image.read("base64").then(function (imageBuffer) {
              return {
                src: "data:" + image.contentType + ";base64," + imageBuffer
              };
            });
          }),
          includeDefaultStyleMap: false,
          styleMap: [
            // Preserve all paragraph styles with their exact formatting
            "p[style-name='Normal'] => p.normal",
            "p[style-name='Title'] => p.title",
            "p[style-name='Subtitle'] => p.subtitle",
            "p[style-name='Heading 1'] => h1.heading1",
            "p[style-name='Heading 2'] => h2.heading2",
            "p[style-name='Heading 3'] => h3.heading3",
            "p[style-name='Header'] => p.header",
            "p[style-name='Footer'] => p.footer",

            // Character styles
            "r[style-name='Strong'] => strong",
            "r[style-name='Emphasis'] => em",
            "r[style-name='Hyperlink'] => a",

            // Preserve all other paragraph styles as divs with classes
            "p => p.paragraph",
            "r => span.run"
          ],
          transformDocument: function (document) {
            // Preserve more document structure
            return document;
          }
        });

        let html = result.value;
        if (!html || html.trim().length === 0) {
          throw new Error(`No content found in ${file.name}`);
        }

        // Create HTML document with exact Word-like styling for perfect layout preservation
        const fullHtml = `
          <!DOCTYPE html>
          <html>
            <head>
              <meta charset="UTF-8">
              <title>Converted Document</title>
              <style>
                /* Page setup to match Word exactly */
                @page {
                  margin: 2.54cm 2.54cm 2.54cm 2.54cm; /* Word default margins */
                  size: 21cm 29.7cm; /* A4 exact dimensions */
                }

                /* Body styling to match Word defaults */
                body {
                  font-family: 'Calibri', sans-serif;
                  font-size: 11pt;
                  line-height: 1.15; /* Word default line spacing */
                  color: #000000;
                  margin: 0;
                  padding: 0;
                  background: white;
                  text-rendering: optimizeLegibility;
                  -webkit-font-smoothing: antialiased;
                }

                /* Reset all margins to match Word behavior */
                * {
                  box-sizing: border-box;
                }

                /* Paragraph styles matching Word exactly */
                p.normal, p.paragraph {
                  margin: 0 0 8pt 0; /* Word default paragraph spacing */
                  text-align: left;
                  font-size: 11pt;
                  line-height: 1.15;
                }

                p.title {
                  margin: 0 0 3pt 0;
                  text-align: center;
                  font-size: 28pt;
                  font-weight: bold;
                  color: #000000;
                  line-height: 1.15;
                }

                p.subtitle {
                  margin: 0 0 10pt 0;
                  text-align: center;
                  font-size: 14pt;
                  font-weight: normal;
                  color: #595959;
                  line-height: 1.15;
                }

                /* Heading styles matching Word defaults */
                h1.heading1 {
                  margin: 12pt 0 0 0;
                  font-size: 16pt;
                  font-weight: bold;
                  color: #2F5496;
                  line-height: 1.15;
                }

                h2.heading2 {
                  margin: 10pt 0 0 0;
                  font-size: 13pt;
                  font-weight: bold;
                  color: #2F5496;
                  line-height: 1.15;
                }

                h3.heading3 {
                  margin: 10pt 0 0 0;
                  font-size: 12pt;
                  font-weight: bold;
                  color: #1F3763;
                  line-height: 1.15;
                }

                /* Image handling - preserve exact positioning */
                img {
                  display: block;
                  max-width: 100%;
                  height: auto;
                  margin: 0;
                  vertical-align: baseline;
                }

                /* Centered images (like profile photos) */
                p:has(img) {
                  text-align: center;
                  margin: 0 0 8pt 0;
                }

                p img {
                  display: inline-block;
                  vertical-align: top;
                }

                /* Tables - match Word table styling */
                table {
                  border-collapse: collapse;
                  border-spacing: 0;
                  width: 100%;
                  margin: 0;
                  font-size: inherit;
                }

                td, th {
                  padding: 0;
                  margin: 0;
                  border: none;
                  vertical-align: top;
                  text-align: left;
                }

                /* Text formatting */
                strong {
                  font-weight: bold;
                }

                em {
                  font-style: italic;
                }

                span.run {
                  /* Preserve inline formatting */
                }

                /* Lists - match Word list formatting */
                ul, ol {
                  margin: 0 0 8pt 0;
                  padding-left: 36pt; /* Word default indent */
                }

                li {
                  margin: 0 0 0 0;
                  line-height: 1.15;
                }

                /* Preserve any inline styles from Word */
                [style] {
                  /* Let inline styles take precedence */
                }

                /* Special handling for Wingdings and Symbol fonts */
                [style*="font-family: Wingdings"],
                [style*="font-family: Symbol"] {
                  font-family: "Wingdings", "Symbol", serif !important;
                }

                /* Preserve text alignment */
                [style*="text-align: center"] {
                  text-align: center !important;
                }

                [style*="text-align: right"] {
                  text-align: right !important;
                }

                [style*="text-align: justify"] {
                  text-align: justify !important;
                }

                /* Preserve font sizes */
                [style*="font-size"] {
                  /* Let inline font-size override */
                }

                /* Preserve colors */
                [style*="color"] {
                  /* Let inline colors override */
                }

                /* Page break handling */
                .page-break {
                  page-break-before: always;
                }

                /* Prevent unwanted page breaks */
                h1, h2, h3, h4, h5, h6 {
                  page-break-after: avoid;
                }

                /* Print optimization */
                @media print {
                  body {
                    margin: 0;
                    padding: 0;
                  }

                  @page {
                    margin: 2.54cm;
                  }
                }
              </style>
            </head>
            <body>
              ${html}
            </body>
          </html>
        `;

        // Use Puppeteer to convert HTML to PDF with Word-like settings
        const browser = await puppeteer.launch({
          headless: true,
          args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-web-security',
            '--font-render-hinting=none'
          ]
        });

        try {
          const page = await browser.newPage();

          // Set viewport to match A4 proportions
          await page.setViewport({
            width: 794,  // A4 width in pixels at 96 DPI
            height: 1123, // A4 height in pixels at 96 DPI
            deviceScaleFactor: 1
          });

          await page.setContent(fullHtml, {
            waitUntil: 'networkidle0',
            timeout: 30000
          });

          // Wait for images to load
          await page.evaluate(() => {
            return Promise.all(Array.from(document.images, img => {
              if (img.complete) return Promise.resolve();
              return new Promise((resolve, reject) => {
                img.addEventListener('load', resolve);
                img.addEventListener('error', reject);
              });
            }));
          });

          const pdfBuffer = await page.pdf({
            width: '21cm',    // A4 width exactly
            height: '29.7cm', // A4 height exactly
            margin: {
              top: '2.54cm',    // Word default margins
              right: '2.54cm',
              bottom: '2.54cm',
              left: '2.54cm'
            },
            printBackground: true,
            preferCSSPageSize: true,
            displayHeaderFooter: false,
            scale: 1.0,
            tagged: false
          });

          await browser.close();

          const filename = `${file.name.replace(/\.[^/.]+$/, '')}.pdf`;
          console.log(`Mammoth + Puppeteer conversion completed: ${pdfBuffer.length} bytes`);

          return new NextResponse(pdfBuffer, {
            status: 200,
            headers: {
              'Content-Type': 'application/pdf',
              'Content-Disposition': `attachment; filename="${filename}"`,
              'Content-Length': pdfBuffer.length.toString(),
            },
          });

        } catch (puppeteerError) {
          await browser.close();
          throw puppeteerError;
        }

      } catch (error) {
        console.error(`Error processing ${file.name}:`, error);
        throw new Error(`Failed to process Word document ${file.name}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    // For multiple files, we'll need to process them individually and merge
    // Since LibreOffice conversion doesn't support merging, we'll process each separately
    else {
      return NextResponse.json(
        { error: 'Multiple file conversion with full layout preservation is not yet supported. Please convert one file at a time for best results.' },
        { status: 400 }
      );
    }

  } catch (error) {
    console.error('Word to PDF conversion error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Conversion failed' },
      { status: 500 }
    );
  }
}

