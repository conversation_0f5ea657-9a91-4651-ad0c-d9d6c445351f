import { NextRequest, NextResponse } from 'next/server';
import mammoth from 'mammoth';
import puppeteer from 'puppeteer';
import * as pdfjsLib from 'pdfjs-dist';

const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

// Configure PDF.js worker
if (typeof window === 'undefined') {
  // Server-side configuration
  pdfjsLib.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjsLib.version}/pdf.worker.min.js`;
}

// Enhanced conversion function using both mammoth and pdfjs-dist
async function convertDocxToPdfAdvanced(buffer: Buffer): Promise<Buffer> {
  try {
    // First, extract content using mammoth with maximum fidelity
    const result = await mammoth.convertToHtml({ buffer }, {
      convertImage: mammoth.images.imgElement(function (image) {
        return image.read("base64").then(function (imageBuffer) {
          return {
            src: "data:" + image.contentType + ";base64," + imageBuffer
          };
        });
      }),
      includeDefaultStyleMap: false,
      styleMap: [
        // Preserve exact Word styling
        "p[style-name='Normal'] => p.normal",
        "p[style-name='Title'] => p.title",
        "p[style-name='Subtitle'] => p.subtitle",
        "p[style-name='Heading 1'] => h1.heading1",
        "p[style-name='Heading 2'] => h2.heading2",
        "p[style-name='Heading 3'] => h3.heading3",
        "r[style-name='Strong'] => strong",
        "r[style-name='Emphasis'] => em",
        "p => p.paragraph",
        "r => span.run"
      ],

    });

    // Create enhanced HTML with Word-perfect styling
    const html = createWordPerfectHTML(result.value);

    // Use Puppeteer with optimized settings for Word-like output
    return await generatePdfWithPuppeteer(html);

  } catch (error) {
    console.error('Advanced conversion failed:', error);
    throw error;
  }
}

function createWordPerfectHTML(content: string): string {
  return `
    <!DOCTYPE html>
    <html>
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Document</title>
        <style>
          /* Exact Word document replication */
          @page {
            size: 21cm 29.7cm;
            margin: 2.54cm;
          }

          body {
            font-family: 'Calibri', 'Arial', sans-serif;
            font-size: 11pt;
            line-height: 1.08;
            color: #000000;
            margin: 0;
            padding: 0;
            background: white;
            text-rendering: optimizeLegibility;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
          }

          /* Reset and normalize */
          * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
          }

          /* Paragraph styles exactly matching Word */
          p.normal, p.paragraph {
            margin: 0 0 8pt 0;
            text-align: left;
            font-size: 11pt;
            line-height: 1.08;
            text-indent: 0;
          }

          p.title {
            margin: 0 0 3pt 0;
            text-align: center;
            font-size: 28pt;
            font-weight: bold;
            color: #000000;
            line-height: 1.08;
          }

          p.subtitle {
            margin: 0 0 10pt 0;
            text-align: center;
            font-size: 14pt;
            font-weight: normal;
            color: #595959;
            line-height: 1.08;
          }

          /* Headings with exact Word colors and spacing */
          h1.heading1 {
            margin: 12pt 0 0 0;
            font-size: 16pt;
            font-weight: bold;
            color: #2F5496;
            line-height: 1.08;
            page-break-after: avoid;
          }

          h2.heading2 {
            margin: 10pt 0 0 0;
            font-size: 13pt;
            font-weight: bold;
            color: #2F5496;
            line-height: 1.08;
            page-break-after: avoid;
          }

          h3.heading3 {
            margin: 10pt 0 0 0;
            font-size: 12pt;
            font-weight: bold;
            color: #1F3763;
            line-height: 1.08;
            page-break-after: avoid;
          }

          /* Image handling for perfect positioning */
          img {
            max-width: 100%;
            height: auto;
            display: block;
            margin: 0 auto;
            page-break-inside: avoid;
          }

          /* Center images in paragraphs */
          p:has(img) {
            text-align: center;
            margin: 0 0 8pt 0;
          }

          /* Tables with Word-like formatting */
          table {
            border-collapse: collapse;
            border-spacing: 0;
            width: 100%;
            margin: 0 0 8pt 0;
            font-size: inherit;
            page-break-inside: avoid;
          }

          td, th {
            padding: 0;
            border: none;
            vertical-align: top;
            text-align: left;
          }

          /* Text formatting */
          strong {
            font-weight: bold;
          }

          em {
            font-style: italic;
          }

          /* Lists with Word spacing */
          ul, ol {
            margin: 0 0 8pt 0;
            padding-left: 36pt;
          }

          li {
            margin: 0;
            line-height: 1.08;
          }

          /* Preserve inline styles */
          [style*="text-align: center"] { text-align: center !important; }
          [style*="text-align: right"] { text-align: right !important; }
          [style*="text-align: justify"] { text-align: justify !important; }
          [style*="font-weight: bold"] { font-weight: bold !important; }
          [style*="font-style: italic"] { font-style: italic !important; }

          /* Special fonts */
          [style*="font-family: Wingdings"] { font-family: "Wingdings", serif !important; }
          [style*="font-family: Symbol"] { font-family: "Symbol", serif !important; }

          /* Page break control */
          .page-break { page-break-before: always; }
          .no-break { page-break-inside: avoid; }
        </style>
      </head>
      <body>
        ${content}
      </body>
    </html>
  `;
}

async function generatePdfWithPuppeteer(html: string): Promise<Buffer> {
  const browser = await puppeteer.launch({
    headless: true,
    args: [
      '--no-sandbox',
      '--disable-setuid-sandbox',
      '--disable-dev-shm-usage',
      '--disable-web-security',
      '--font-render-hinting=none',
      '--disable-font-subpixel-positioning'
    ]
  });

  try {
    const page = await browser.newPage();

    // Set exact A4 dimensions
    await page.setViewport({
      width: 794,   // A4 width at 96 DPI
      height: 1123, // A4 height at 96 DPI
      deviceScaleFactor: 1
    });

    await page.setContent(html, {
      waitUntil: 'networkidle0',
      timeout: 30000
    });

    // Wait for all images to load
    await page.evaluate(() => {
      return Promise.all(Array.from(document.images, img => {
        if (img.complete) return Promise.resolve();
        return new Promise((resolve, reject) => {
          img.addEventListener('load', resolve);
          img.addEventListener('error', reject);
        });
      }));
    });

    // Generate PDF with Word-like settings
    const pdfBuffer = await page.pdf({
      width: '21cm',
      height: '29.7cm',
      margin: {
        top: '2.54cm',
        right: '2.54cm',
        bottom: '2.54cm',
        left: '2.54cm'
      },
      printBackground: true,
      preferCSSPageSize: true,
      displayHeaderFooter: false,
      scale: 1.0,
      tagged: false
    });

    return Buffer.from(pdfBuffer);

  } finally {
    await browser.close();
  }
}

export async function POST(request: NextRequest) {
  console.log('Word to PDF conversion request received');

  try {
    const formData = await request.formData();
    const files = formData.getAll('files') as File[];

    if (!files || files.length === 0) {
      return NextResponse.json(
        { error: 'No files provided' },
        { status: 400 }
      );
    }

    // Validate files
    const validTypes = [
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/msword'
    ];

    for (const file of files) {
      if (file.size > MAX_FILE_SIZE) {
        return NextResponse.json(
          { error: `File ${file.name} exceeds 10MB limit` },
          { status: 400 }
        );
      }

      if (!validTypes.includes(file.type)) {
        return NextResponse.json(
          { error: `Invalid file type: ${file.name}. Expected Word document (.docx, .doc)` },
          { status: 400 }
        );
      }
    }

    console.log(`Converting ${files.length} Word document(s) to PDF`);

    // For single file conversion with full layout preservation
    if (files.length === 1) {
      const file = files[0];

      try {
        const arrayBuffer = await file.arrayBuffer();
        const buffer = Buffer.from(arrayBuffer);

        // Use the advanced conversion function for better layout preservation
        console.log('Converting DOCX to PDF using advanced mammoth + pdfjs-dist + puppeteer...');

        const pdfBuffer = await convertDocxToPdfAdvanced(buffer);
        const filename = `${file.name.replace(/\.[^/.]+$/, '')}.pdf`;

        console.log(`Advanced Word to PDF conversion completed: ${pdfBuffer.length} bytes`);

        return new NextResponse(pdfBuffer, {
          status: 200,
          headers: {
            'Content-Type': 'application/pdf',
            'Content-Disposition': `attachment; filename="${filename}"`,
            'Content-Length': pdfBuffer.length.toString(),
          },
        });

      } catch (error) {
        console.error(`Error processing ${file.name}:`, error);
        throw new Error(`Failed to process Word document ${file.name}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    // For multiple files, we'll need to process them individually and merge
    // Since LibreOffice conversion doesn't support merging, we'll process each separately
    else {
      return NextResponse.json(
        { error: 'Multiple file conversion with full layout preservation is not yet supported. Please convert one file at a time for best results.' },
        { status: 400 }
      );
    }

  } catch (error) {
    console.error('Word to PDF conversion error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Conversion failed' },
      { status: 500 }
    );
  }
}

