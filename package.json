{"name": "src", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-context-menu": "^2.2.6", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@tiptap/extension-color": "^2.23.0", "@tiptap/extension-highlight": "^2.23.0", "@tiptap/extension-image": "^2.23.0", "@tiptap/extension-link": "^2.23.0", "@tiptap/extension-text-align": "^2.23.0", "@tiptap/extension-text-style": "^2.23.0", "@tiptap/pm": "^2.23.0", "@tiptap/react": "^2.23.0", "@tiptap/starter-kit": "^2.23.0", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "@types/multer": "^1.4.13", "archiver": "^7.0.1", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "cloudinary": "^2.7.0", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^3.6.0", "docx": "^9.5.1", "docx-parser": "^0.2.1", "docx-pdf": "^0.0.1", "dotenv": "^17.0.0", "embla-carousel-react": "^8.5.2", "framer-motion": "^12.6.3", "gray-matter": "^4.0.3", "input-otp": "^1.4.2", "jsonwebtoken": "^9.0.2", "jszip": "^3.10.1", "lucide-react": "^0.487.0", "mammoth": "^1.9.1", "mongoose": "^8.16.1", "multer": "^2.0.1", "next": "15.2.4", "next-auth": "^5.0.0-beta.29", "next-themes": "^0.4.6", "pdf-lib": "^1.17.1", "pdf-parse": "^1.1.1", "pptxgenjs": "^4.0.1", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.55.0", "react-resizable-panels": "^2.1.7", "recharts": "^2.15.1", "rehype-sanitize": "^6.0.0", "remark": "^15.0.1", "remark-html": "^16.0.1", "sharp": "^0.34.3", "sonner": "^2.0.3", "tailwind-merge": "^3.1.0", "tw-animate-css": "^1.2.5", "vaul": "^1.1.2", "xlsx": "^0.18.5", "zod": "^3.24.2", "zustand": "^5.0.6"}, "devDependencies": {"@babel/eslint-parser": "^7.27.1", "@eslint/eslintrc": "^3", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/node": "^20", "@types/pdf-parse": "^1.1.5", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.2.4", "jsdom": "^26.0.0", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "typescript": "^5", "vitest": "^3.1.1"}}