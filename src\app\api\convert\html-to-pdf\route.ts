import { NextRequest, NextResponse } from 'next/server';
import { PDFDocument, rgb, StandardFonts } from 'pdf-lib';

const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

export async function POST(request: NextRequest) {
  console.log('HTML to PDF conversion request received');

  try {
    const formData = await request.formData();
    const files = formData.getAll('files') as File[];

    if (!files || files.length === 0) {
      return NextResponse.json(
        { error: 'No files provided' },
        { status: 400 }
      );
    }

    // Validate files
    const validTypes = ['text/html', 'text/plain'];

    for (const file of files) {
      if (file.size > MAX_FILE_SIZE) {
        return NextResponse.json(
          { error: `File ${file.name} exceeds 10MB limit` },
          { status: 400 }
        );
      }

      if (!validTypes.includes(file.type)) {
        return NextResponse.json(
          { error: `Invalid file type: ${file.name}. Expected HTML file (.html)` },
          { status: 400 }
        );
      }
    }

    console.log(`Converting ${files.length} HTML file(s) to PDF`);

    const pdfDoc = await PDFDocument.create();
    const font = await pdfDoc.embedFont(StandardFonts.Helvetica);
    const boldFont = await pdfDoc.embedFont(StandardFonts.HelveticaBold);

    for (const file of files) {
      try {
        const htmlContent = await file.text();

        // Strip HTML tags for basic text extraction
        let textContent = htmlContent
          .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
          .replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, '')
          .replace(/<[^>]*>/g, ' ')
          .replace(/\s+/g, ' ')
          .trim();

        // Clean text to remove problematic characters that WinAnsi can't encode
        textContent = textContent
          .replace(/\t/g, '    ') // Replace tabs with 4 spaces
          .replace(/[\u0000-\u0008\u000B-\u000C\u000E-\u001F\u007F-\u009F]/g, '') // Remove control characters
          .replace(/[^\u0020-\u007E\u00A0-\u00FF]/g, '?'); // Replace non-WinAnsi characters with ?

        if (!textContent) {
          throw new Error('No text content found in HTML');
        }

        // Create pages and add content
        let currentPage = pdfDoc.addPage([612, 792]); // Letter size
        let yPosition = 750;
        const margin = 50;
        const lineHeight = 14;
        const maxWidth = 512; // Page width minus margins

        // Add title
        currentPage.drawText('HTML Document Conversion', {
          x: margin,
          y: yPosition,
          size: 16,
          font: boldFont,
          color: rgb(0, 0, 0),
        });

        yPosition -= 30;

        // Split text into paragraphs
        const paragraphs = textContent.split(/\n+/).filter(p => p.trim().length > 0);

        for (const paragraph of paragraphs) {
          // Handle long paragraphs by wrapping text
          const words = paragraph.split(' ');
          let currentLine = '';

          for (const word of words) {
            const testLine = currentLine ? `${currentLine} ${word}` : word;
            const textWidth = font.widthOfTextAtSize(testLine, 12);

            if (textWidth > maxWidth && currentLine) {
              // Draw current line and start new one
              currentPage.drawText(currentLine, {
                x: margin,
                y: yPosition,
                size: 12,
                font: font,
                color: rgb(0, 0, 0),
              });

              yPosition -= lineHeight;
              currentLine = word;

              // Check if we need a new page
              if (yPosition < 50) {
                currentPage = pdfDoc.addPage([612, 792]);
                yPosition = 750;
              }
            } else {
              currentLine = testLine;
            }
          }

          // Draw the remaining text
          if (currentLine) {
            currentPage.drawText(currentLine, {
              x: margin,
              y: yPosition,
              size: 12,
              font: font,
              color: rgb(0, 0, 0),
            });

            yPosition -= lineHeight * 1.5; // Extra space between paragraphs

            // Check if we need a new page
            if (yPosition < 50) {
              currentPage = pdfDoc.addPage([612, 792]);
              yPosition = 750;
            }
          }
        }

      } catch (error) {
        console.error(`Error processing ${file.name}:`, error);
        throw new Error(`Failed to process HTML file ${file.name}`);
      }
    }

    const pdfBytes = await pdfDoc.save();
    const filename = files.length === 1 ?
      `${files[0].name.replace(/\.[^/.]+$/, '')}.pdf` :
      'converted-html.pdf';

    console.log(`HTML to PDF conversion completed: ${pdfBytes.length} bytes`);

    return new NextResponse(Buffer.from(pdfBytes), {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="${filename}"`,
        'Content-Length': pdfBytes.length.toString(),
      },
    });

  } catch (error) {
    console.error('HTML to PDF conversion error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Conversion failed' },
      { status: 500 }
    );
  }
}
