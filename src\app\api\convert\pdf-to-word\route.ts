import { NextRequest, NextResponse } from 'next/server';
import { PDFDocument } from 'pdf-lib';
import { Document, Packer, Paragraph, TextRun, HeadingLevel } from 'docx';
import pdf from 'pdf-parse';

const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

export async function POST(request: NextRequest) {
  console.log('PDF to Word conversion request received');

  try {
    const formData = await request.formData();
    const files = formData.getAll('files') as File[];

    if (!files || files.length === 0) {
      return NextResponse.json(
        { error: 'No files provided' },
        { status: 400 }
      );
    }

    if (files.length > 1) {
      return NextResponse.json(
        { error: 'Only one PDF file can be processed at a time' },
        { status: 400 }
      );
    }

    const file = files[0];

    if (file.size > MAX_FILE_SIZE) {
      return NextResponse.json(
        { error: `File ${file.name} exceeds 10MB limit` },
        { status: 400 }
      );
    }

    if (file.type !== 'application/pdf') {
      return NextResponse.json(
        { error: `Invalid file type: ${file.name}. Expected PDF file` },
        { status: 400 }
      );
    }

    console.log(`Converting PDF to Word: ${file.name}`);

    try {
      const arrayBuffer = await file.arrayBuffer();
      const pdfBuffer = Buffer.from(arrayBuffer);

      // Extract actual text content from PDF using pdf-parse
      let extractedText = '';
      let pageCount = 0;

      try {
        const pdfData = await pdf(pdfBuffer);
        extractedText = pdfData.text;
        pageCount = pdfData.numpages;

        console.log(`Extracted ${extractedText.length} characters from ${pageCount} pages`);
      } catch (parseError) {
        console.warn('pdf-parse failed, falling back to pdf-lib:', parseError);

        // Fallback to pdf-lib for basic info
        const pdfDoc = await PDFDocument.load(pdfBuffer);
        const pages = pdfDoc.getPages();
        pageCount = pages.length;
        extractedText = `PDF Document with ${pageCount} pages\nOriginal file: ${file.name}\nText extraction failed - this may be a scanned document or contain complex formatting.`;
      }

      if (!extractedText || extractedText.trim().length === 0) {
        throw new Error('No text content could be extracted from PDF');
      }

      // Clean and structure the extracted text
      const cleanText = extractedText
        .replace(/\r\n/g, '\n')
        .replace(/\r/g, '\n')
        .replace(/\n{3,}/g, '\n\n') // Reduce multiple line breaks
        .trim();

      // Split into paragraphs and filter empty ones
      const paragraphs = cleanText.split('\n\n').filter(p => p.trim().length > 0);

      // Create Word document with proper structure
      const documentChildren = [];

      // Add document header
      documentChildren.push(
        new Paragraph({
          children: [
            new TextRun({
              text: 'PDF to Word Conversion',
              bold: true,
              size: 32,
            })
          ],
          heading: HeadingLevel.HEADING_1,
        }),
        new Paragraph({
          children: [
            new TextRun({
              text: `Converted from: ${file.name}`,
              italics: true,
            })
          ],
        }),
        new Paragraph({
          children: [
            new TextRun({
              text: `Pages: ${pageCount} | Size: ${(file.size / 1024).toFixed(2)} KB | Date: ${new Date().toLocaleDateString()}`,
              size: 20,
              color: '666666',
            })
          ],
        }),
        new Paragraph({ text: '' }), // Empty paragraph for spacing
      );

      // Add extracted content
      paragraphs.forEach((paragraph, index) => {
        // Check if paragraph looks like a heading (short, all caps, or starts with number)
        const isHeading = paragraph.length < 100 &&
          (paragraph === paragraph.toUpperCase() ||
            /^\d+\.?\s/.test(paragraph) ||
            paragraph.split(' ').length <= 8);

        if (isHeading && index < paragraphs.length - 1) {
          documentChildren.push(
            new Paragraph({
              children: [
                new TextRun({
                  text: paragraph,
                  bold: true,
                  size: 24,
                })
              ],
              heading: HeadingLevel.HEADING_2,
            })
          );
        } else {
          // Split long paragraphs into sentences for better formatting
          const sentences = paragraph.split(/(?<=[.!?])\s+/);
          const textRuns = sentences.map((sentence, sentenceIndex) =>
            new TextRun({
              text: sentence + (sentenceIndex < sentences.length - 1 ? ' ' : ''),
            })
          );

          documentChildren.push(
            new Paragraph({
              children: textRuns,
            })
          );
        }
      });

      // Create Word document
      const doc = new Document({
        sections: [{
          properties: {},
          children: documentChildren,
        }],
      });

      const docxBuffer = await Packer.toBuffer(doc);
      const filename = `${file.name.replace(/\.[^/.]+$/, '')}.docx`;

      console.log(`PDF to Word conversion completed: ${docxBuffer.length} bytes`);

      return new NextResponse(docxBuffer, {
        status: 200,
        headers: {
          'Content-Type': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          'Content-Disposition': `attachment; filename="${filename}"`,
          'Content-Length': docxBuffer.length.toString(),
        },
      });

    } catch (error) {
      console.error('PDF to Word conversion error:', error);
      throw new Error(`Failed to convert PDF to Word: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

  } catch (error) {
    console.error('PDF to Word conversion error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Conversion failed' },
      { status: 500 }
    );
  }
}
