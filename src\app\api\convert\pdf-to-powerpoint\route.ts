import { NextRequest, NextResponse } from 'next/server';
import { PDFDocument } from 'pdf-lib';
import PptxGenJS from 'pptxgenjs';
import pdf from 'pdf-parse';

const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

export async function POST(request: NextRequest) {
  console.log('PDF to PowerPoint conversion request received');

  try {
    const formData = await request.formData();
    const files = formData.getAll('files') as File[];

    if (!files || files.length === 0) {
      return NextResponse.json(
        { error: 'No files provided' },
        { status: 400 }
      );
    }

    if (files.length > 1) {
      return NextResponse.json(
        { error: 'Only one PDF file can be processed at a time' },
        { status: 400 }
      );
    }

    const file = files[0];

    if (file.size > MAX_FILE_SIZE) {
      return NextResponse.json(
        { error: `File ${file.name} exceeds 10MB limit` },
        { status: 400 }
      );
    }

    if (file.type !== 'application/pdf') {
      return NextResponse.json(
        { error: `Invalid file type: ${file.name}. Expected PDF file` },
        { status: 400 }
      );
    }

    console.log(`Converting PDF to PowerPoint: ${file.name}`);

    try {
      const arrayBuffer = await file.arrayBuffer();
      const pdfBuffer = Buffer.from(arrayBuffer);

      // Extract actual text content from PDF using pdf-parse
      let extractedText = '';
      let pageCount = 0;

      try {
        const pdfData = await pdf(pdfBuffer);
        extractedText = pdfData.text;
        pageCount = pdfData.numpages;

        console.log(`Extracted ${extractedText.length} characters from ${pageCount} pages`);
      } catch (parseError) {
        console.warn('pdf-parse failed, falling back to pdf-lib:', parseError);

        // Fallback to pdf-lib for basic info
        const pdfDoc = await PDFDocument.load(pdfBuffer);
        const pages = pdfDoc.getPages();
        pageCount = pages.length;
        extractedText = `PDF Document with ${pageCount} pages\nOriginal file: ${file.name}\nText extraction failed - this may be a scanned document.`;
      }

      // Create PowerPoint presentation
      const pptx = new PptxGenJS();

      // Title slide
      const titleSlide = pptx.addSlide();
      titleSlide.addText('PDF to PowerPoint Conversion', {
        x: 0.5,
        y: 1,
        w: 9,
        h: 1,
        fontSize: 32,
        bold: true,
        color: '363636',
        align: 'center'
      });

      titleSlide.addText(`Converted from: ${file.name}`, {
        x: 0.5,
        y: 2.5,
        w: 9,
        h: 0.5,
        fontSize: 18,
        color: '666666',
        align: 'center'
      });

      titleSlide.addText(`${pageCount} page${pageCount !== 1 ? 's' : ''} • ${(file.size / 1024).toFixed(2)} KB • ${extractedText.length} characters`, {
        x: 0.5,
        y: 3.2,
        w: 9,
        h: 0.5,
        fontSize: 14,
        color: '999999',
        align: 'center'
      });

      // Process extracted text into slides
      if (extractedText && extractedText.trim().length > 0) {
        // Split text into logical sections for slides
        const sections = extractedText
          .split(/\n\s*\n/) // Split by double line breaks (paragraphs)
          .filter(section => section.trim().length > 0)
          .map(section => section.trim());

        // Group sections into slides (aim for ~300-500 characters per slide)
        const slides: string[] = [];
        let currentSlide = '';

        sections.forEach(section => {
          if (currentSlide.length + section.length < 500) {
            currentSlide += (currentSlide ? '\n\n' : '') + section;
          } else {
            if (currentSlide) slides.push(currentSlide);
            currentSlide = section;
          }
        });

        if (currentSlide) slides.push(currentSlide);

        // Create slides from content
        slides.forEach((slideContent, index) => {
          const slide = pptx.addSlide();

          // Try to extract a title from the first line
          const lines = slideContent.split('\n');
          const firstLine = lines[0];
          const isTitle = firstLine.length < 80 &&
            (firstLine === firstLine.toUpperCase() ||
              /^\d+\.?\s/.test(firstLine) ||
              firstLine.split(' ').length <= 8);

          let title = isTitle ? firstLine : `Content ${index + 1}`;
          let content = isTitle ? lines.slice(1).join('\n') : slideContent;

          // Add slide title
          slide.addText(title, {
            x: 0.5,
            y: 0.5,
            w: 9,
            h: 0.8,
            fontSize: 24,
            bold: true,
            color: '363636'
          });

          // Add content
          if (content.trim()) {
            slide.addText(content, {
              x: 0.5,
              y: 1.5,
              w: 9,
              h: 5,
              fontSize: 14,
              color: '666666',
              valign: 'top'
            });
          }
        });

        // Add a content overview slide
        const overviewSlide = pptx.addSlide();
        overviewSlide.addText('Content Overview', {
          x: 0.5,
          y: 0.5,
          w: 9,
          h: 0.8,
          fontSize: 24,
          bold: true,
          color: '363636'
        });

        const overviewContent = [
          `Total slides created: ${slides.length}`,
          `Original pages: ${pageCount}`,
          `Characters processed: ${extractedText.length}`,
          '',
          'Content breakdown:',
          ...slides.slice(0, 10).map((slide, i) => {
            const preview = slide.split('\n')[0].substring(0, 60);
            return `${i + 1}. ${preview}${slide.length > 60 ? '...' : ''}`;
          })
        ];

        if (slides.length > 10) {
          overviewContent.push(`... and ${slides.length - 10} more slides`);
        }

        overviewSlide.addText(overviewContent.join('\n'), {
          x: 0.5,
          y: 1.5,
          w: 9,
          h: 5,
          fontSize: 12,
          color: '666666',
          valign: 'top'
        });
      } else {
        // Fallback slide if no text could be extracted
        const slide = pptx.addSlide();
        slide.addText('Content Extraction Failed', {
          x: 0.5,
          y: 2,
          w: 9,
          h: 1,
          fontSize: 24,
          bold: true,
          color: 'CC0000'
        });

        slide.addText('This PDF may contain scanned images or complex formatting that requires OCR processing.', {
          x: 0.5,
          y: 3,
          w: 9,
          h: 2,
          fontSize: 16,
          color: '666666',
          align: 'center'
        });
      }

      // Summary slide
      const summarySlide = pptx.addSlide();
      summarySlide.addText('Conversion Summary', {
        x: 0.5,
        y: 0.5,
        w: 9,
        h: 0.8,
        fontSize: 24,
        bold: true,
        color: '363636'
      });

      const summaryText = [
        `Successfully processed ${pageCount} page${pageCount !== 1 ? 's' : ''} from ${file.name}`,
        '',
        `Conversion completed on: ${new Date().toLocaleDateString()}`,
        `Original file size: ${(file.size / 1024).toFixed(2)} KB`,
        '',
        'This presentation contains placeholder content.',
        'A production system would extract and preserve:',
        '• All text content with formatting',
        '• Images, charts, and graphics',
        '• Tables and structured data',
        '• Layout and styling information'
      ];

      summarySlide.addText(summaryText.join('\n'), {
        x: 0.5,
        y: 1.5,
        w: 9,
        h: 5,
        fontSize: 14,
        color: '666666',
        valign: 'top'
      });

      const pptxBuffer = await pptx.write({ outputType: 'nodebuffer' }) as Buffer;
      const filename = `${file.name.replace(/\.[^/.]+$/, '')}.pptx`;

      console.log(`PDF to PowerPoint conversion completed: ${pptxBuffer.length} bytes`);

      return new NextResponse(pptxBuffer, {
        status: 200,
        headers: {
          'Content-Type': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
          'Content-Disposition': `attachment; filename="${filename}"`,
          'Content-Length': pptxBuffer.length.toString(),
        },
      });

    } catch (error) {
      console.error('PDF to PowerPoint conversion error:', error);
      throw new Error(`Failed to convert PDF to PowerPoint: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

  } catch (error) {
    console.error('PDF to PowerPoint conversion error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Conversion failed' },
      { status: 500 }
    );
  }
}
