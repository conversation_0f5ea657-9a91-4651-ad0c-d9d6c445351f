import { NextRequest, NextResponse } from 'next/server';
import { PDFDocument } from 'pdf-lib';
import * as XLSX from 'xlsx';
import pdf from 'pdf-parse';

const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

export async function POST(request: NextRequest) {
  console.log('PDF to Excel conversion request received');

  try {
    const formData = await request.formData();
    const files = formData.getAll('files') as File[];

    if (!files || files.length === 0) {
      return NextResponse.json(
        { error: 'No files provided' },
        { status: 400 }
      );
    }

    if (files.length > 1) {
      return NextResponse.json(
        { error: 'Only one PDF file can be processed at a time' },
        { status: 400 }
      );
    }

    const file = files[0];

    if (file.size > MAX_FILE_SIZE) {
      return NextResponse.json(
        { error: `File ${file.name} exceeds 10MB limit` },
        { status: 400 }
      );
    }

    if (file.type !== 'application/pdf') {
      return NextResponse.json(
        { error: `Invalid file type: ${file.name}. Expected PDF file` },
        { status: 400 }
      );
    }

    console.log(`Converting PDF to Excel: ${file.name}`);

    try {
      const arrayBuffer = await file.arrayBuffer();
      const pdfBuffer = Buffer.from(arrayBuffer);

      // Extract actual text content from PDF using pdf-parse
      let extractedText = '';
      let pageCount = 0;

      try {
        const pdfData = await pdf(pdfBuffer);
        extractedText = pdfData.text;
        pageCount = pdfData.numpages;

        console.log(`Extracted ${extractedText.length} characters from ${pageCount} pages`);
      } catch (parseError) {
        console.warn('pdf-parse failed, falling back to pdf-lib:', parseError);

        // Fallback to pdf-lib for basic info
        const pdfDoc = await PDFDocument.load(pdfBuffer);
        const pages = pdfDoc.getPages();
        pageCount = pages.length;
        extractedText = `PDF Document with ${pageCount} pages\nOriginal file: ${file.name}\nText extraction failed - this may be a scanned document.`;
      }

      // Create workbook
      const workbook = XLSX.utils.book_new();

      // Create summary sheet
      const summaryData = [
        ['PDF to Excel Conversion Summary'],
        [''],
        ['Original File', file.name],
        ['File Size (KB)', (file.size / 1024).toFixed(2)],
        ['Total Pages', pageCount],
        ['Characters Extracted', extractedText.length],
        ['Conversion Date', new Date().toLocaleDateString()],
        ['Conversion Time', new Date().toLocaleTimeString()],
        [''],
        ['Text Preview (First 500 characters):'],
        [extractedText.substring(0, 500) + (extractedText.length > 500 ? '...' : '')],
      ];

      // Add notes about production conversion
      summaryData.push(
        [''],
        ['Production Conversion Notes'],
        [''],
        ['Feature', 'Status', 'Description'],
        ['Text Extraction', 'Available', 'Use pdf-parse or similar libraries'],
        ['Table Detection', 'Available', 'Use tabula-js or custom algorithms'],
        ['Image Extraction', 'Available', 'Use pdf2pic or pdf-poppler'],
        ['Layout Preservation', 'Available', 'Custom parsing and positioning'],
        ['Data Structure', 'Available', 'Intelligent table and data recognition'],
        [''],
        ['This conversion shows document metadata.'],
        ['Production systems would extract actual content:'],
        ['• Text content with proper formatting'],
        ['• Tables converted to Excel format'],
        ['• Data structures preserved'],
        ['• Images embedded or referenced'],
        ['• Multiple sheets for complex documents']
      );

      // Create summary worksheet
      const summaryWorksheet = XLSX.utils.aoa_to_sheet(summaryData);
      XLSX.utils.book_append_sheet(workbook, summaryWorksheet, 'Summary');

      // Process extracted text into structured data
      if (extractedText && extractedText.trim().length > 0) {
        // Split text into lines and try to detect tabular data
        const lines = extractedText.split('\n').filter(line => line.trim().length > 0);

        // Create main content sheet
        const contentData = [['Line Number', 'Content', 'Word Count', 'Character Count']];

        lines.forEach((line, index) => {
          const trimmedLine = line.trim();
          const wordCount = trimmedLine.split(/\s+/).length;
          const charCount = trimmedLine.length;

          contentData.push([
            index + 1,
            trimmedLine,
            wordCount,
            charCount
          ]);
        });

        const contentWorksheet = XLSX.utils.aoa_to_sheet(contentData);
        XLSX.utils.book_append_sheet(workbook, contentWorksheet, 'Extracted_Content');

        // Try to detect and extract table-like data
        const tableData = [['Detected Tables and Structured Data']];
        let foundTables = false;

        lines.forEach((line, index) => {
          // Look for lines that might be table headers or data
          if (line.includes('\t') || /\s{3,}/.test(line)) {
            if (!foundTables) {
              tableData.push([''], ['Table-like content detected:'], ['Line', 'Content']);
              foundTables = true;
            }

            const cells = line.includes('\t') ? line.split('\t') : line.split(/\s{3,}/);
            if (cells.length > 1) {
              tableData.push([index + 1, ...cells.slice(0, 5)]); // Limit to 5 columns
            }
          }
        });

        if (!foundTables) {
          tableData.push([''], ['No clear table structure detected in the PDF.']);
          tableData.push(['The content appears to be primarily text-based.']);
        }

        const tableWorksheet = XLSX.utils.aoa_to_sheet(tableData);
        XLSX.utils.book_append_sheet(workbook, tableWorksheet, 'Table_Analysis');

        // Create word frequency analysis
        const words = extractedText.toLowerCase()
          .replace(/[^\w\s]/g, ' ')
          .split(/\s+/)
          .filter(word => word.length > 3);

        const wordFreq: Record<string, number> = {};
        words.forEach(word => {
          wordFreq[word] = (wordFreq[word] || 0) + 1;
        });

        const sortedWords = Object.entries(wordFreq)
          .sort(([, a], [, b]) => b - a)
          .slice(0, 50); // Top 50 words

        const wordFreqData = [
          ['Word Frequency Analysis'],
          [''],
          ['Word', 'Frequency', 'Percentage']
        ];

        const totalWords = words.length;
        sortedWords.forEach(([word, count]) => {
          const percentage = ((count / totalWords) * 100).toFixed(2);
          wordFreqData.push([word, count, `${percentage}%`]);
        });

        const wordFreqWorksheet = XLSX.utils.aoa_to_sheet(wordFreqData);
        XLSX.utils.book_append_sheet(workbook, wordFreqWorksheet, 'Word_Analysis');
      }

      // Generate Excel buffer
      const excelBuffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
      const filename = `${file.name.replace(/\.[^/.]+$/, '')}.xlsx`;

      console.log(`PDF to Excel conversion completed: ${excelBuffer.length} bytes`);

      return new NextResponse(excelBuffer, {
        status: 200,
        headers: {
          'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          'Content-Disposition': `attachment; filename="${filename}"`,
          'Content-Length': excelBuffer.length.toString(),
        },
      });

    } catch (error) {
      console.error('PDF to Excel conversion error:', error);
      throw new Error(`Failed to convert PDF to Excel: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

  } catch (error) {
    console.error('PDF to Excel conversion error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Conversion failed' },
      { status: 500 }
    );
  }
}
